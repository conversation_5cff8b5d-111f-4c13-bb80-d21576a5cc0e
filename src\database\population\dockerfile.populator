# Dockerfile para o serviço de população do banco de dados
FROM python:3.9-slim-buster

# Instalar dependências do sistema necessárias para PostgreSQL client
RUN apt-get update && apt-get install -y \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Definir diretório de trabalho
WORKDIR /app

# Copiar requirements primeiro para aproveitar cache do Docker
COPY database/population/requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Copiar scripts Python
COPY database/population/*.py /app/
COPY database/population/db_config.json /app/

# Criar diretórios necessários
RUN mkdir -p /app/scripts /app/dataset

# Copiar scripts SQL
COPY database/scripts/ /app/scripts/

# Copiar arquivo CSV
COPY dataset/PosicaoIntelli.csv /app/dataset/

# Comando padrão (será sobrescrito pelo docker-compose)
CMD ["python", "setup_and_populate.py"]

