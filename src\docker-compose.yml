services:
  db:
    image: postgres:16 # aqui, utilizamos a imagem oficial do postgres na versão 16
    restart: always    # aqui é a política de recarregamento, caso o conteiner pare, ele tenta reiniciar
    env_file:
      - ./database/.env
    ports:
      - "15432:5432" # 1o num= a porta na maquina host (seu pc), 2o= o outro é a porta interna no container
    volumes:
      - ./database/scripts/01_create_database.sql:/docker-entrypoint-initdb.d/01_create_database.sql
      - ./database/scripts/02_initial_data.sql:/docker-entrypoint-initdb.d/02_initial_data.sql
      - pgdata:/var/lib/postgresql/data
    networks:
      - app-network
    healthcheck: # Adiciona healthcheck para garantir que o banco esteja pronto
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
      interval: 5s
      timeout: 5s
      retries: 5

  mongo:
    image: mongo
    restart: always
    env_file:
      - ./database/.env
    ports:
      - "34567:27017"
    volumes:
      - mongodata:/data/db  
    networks:
      - app-network

  # Serviço Python para modelo SVD baseado no notebook
  svd-service:
    build:
      context: ./apps/api_classification/services
      dockerfile: Dockerfile.svd
    ports:
      - "8500:5000"
    environment:
      - FLASK_ENV=production
      - PORT=5000
    volumes:
      - ./models:/app/models:ro
    networks:
      - app-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  classification-service:
    build:
      context: ./apps/api_classification
      dockerfile: Dockerfile
    ports:
      - "18501:8080"  # Porta externa:interna
    env_file:
      - ./apps/api_classification/.env
    environment:
      - SVDModel__PythonServiceUrl=http://svd-service:5000
    depends_on:
      - db
      - mongo
      - svd-service
    networks:
      - app-network

  recommendation-service:
    build:
      context: ./apps/api_ml_recommender/RecommendationService
      dockerfile: Dockerfile
    ports:
      - "18502:80"  # Porta externa:interna
    env_file:
      - ./apps/api_ml_recommender/RecommendationService/.env
    depends_on:
      - db
      - mongo
    networks:
      - app-network

  api_data_presentation:
    build:
      context: ./apps/api_data_presentation
      dockerfile: api_data_presentation/Dockerfile
    ports:
      - "18503:8080"
    env_file:
      - ./apps/api_data_presentation/api_data_presentation/.env
    depends_on:
      - db
      - mongo
    networks:
      - app-network

  # SERVIÇO: População do banco de dados
  db-populator:
    build:
      context: . # Contexto de build corrigido para raiz
      dockerfile: database/population/dockerfile.populator   # Nome do Dockerfile
    depends_on:
      db:
        condition: service_healthy # Espera o banco estar pronto
    networks:
      - app-network
    environment:
      # Configurações do banco para Docker
      DB_HOST: db # Nome do serviço do banco
      DB_DATABASE: mean_girls
      DB_USER: inteli
      DB_PASSWORD: inteli@123
      DB_PORT: 5432
    # O serviço roda uma vez e para (não fica em loop)
    restart: "no"

volumes:
  pgdata:
  mongodata:

networks:
  app-network:
    driver: bridge
