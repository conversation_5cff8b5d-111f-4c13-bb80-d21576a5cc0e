#!/usr/bin/env python3
"""
Script de configuração e execução da população do banco de dados
com dados do CSV e API randomuser.me
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def install_dependencies():
    """Instala as dependências necessárias"""
    print("📦 Instalando dependências...")
    
    dependencies = [
        'pandas',
        'requests', 
        'psycopg2-binary'
    ]
    
    for dep in dependencies:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"✅ {dep} instalado com sucesso")
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro ao instalar {dep}: {e}")
            return False
    
    return True

def create_config_file():
    """Cria arquivo de configuração do banco de dados"""
    script_dir = Path(__file__).parent  # Pasta onde está o script
    config_path = script_dir / 'db_config.json'
    
    # Prioriza variáveis de ambiente para Docker, mas mantém compatibilidade local
    db_host = os.getenv('DB_HOST', 'localhost')
    db_database = os.getenv('DB_DATABASE', 'mean_girls')
    db_user = os.getenv('DB_USER', 'inteli')
    db_password = os.getenv('DB_PASSWORD', 'inteli@123')
    db_port = os.getenv('DB_PORT', '5432')

    config = {
        "host": db_host,
        "database": db_database,
        "user": db_user,
        "password": db_password,
        "port": db_port
    }
    
    # Se estamos no Docker (variáveis de ambiente definidas), criar/atualizar config
    if any(os.getenv(var) for var in ['DB_HOST', 'DB_DATABASE', 'DB_USER', 'DB_PASSWORD', 'DB_PORT']):
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        print("✅ Arquivo db_config.json criado/atualizado com variáveis de ambiente")
        print(f"Configuração usada: host={db_host}, database={db_database}, user={db_user}")
    elif config_path.exists():
        print("📋 Usando arquivo de configuração existente (modo local)")
    else:
        # Criar arquivo padrão para desenvolvimento local
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        print("✅ Arquivo db_config.json criado com configurações padrão")
        print("⚠️  IMPORTANTE: Edite db_config.json com suas credenciais do banco se necessário")
    
    return True

def check_csv_file():
    """Verifica se o arquivo CSV existe"""
    # Detectar se estamos no Docker ou ambiente local
    docker_csv_path = Path('./dataset/PosicaoIntelli.csv')
    local_csv_path = Path('../../dataset/PosicaoIntelli.csv')
    
    # Tentar primeiro o caminho do Docker
    if docker_csv_path.exists():
        print(f"✅ Arquivo CSV encontrado (Docker): {docker_csv_path}")
        return True
    
    # Tentar caminho local
    if local_csv_path.exists():
        print(f"✅ Arquivo CSV encontrado (local): {local_csv_path}")
        return True
    
    # Tentar caminho absoluto original (fallback)
    original_path = Path('/Users/<USER>/Documents/GitHub/2025-1B-T13-ES06-G02/src/dataset/PosicaoIntelli.csv')
    if original_path.exists():
        print(f"✅ Arquivo CSV encontrado (caminho original): {original_path}")
        return True
    
    print("❌ Arquivo CSV não encontrado em nenhum local:")
    print(f"  - Docker: {docker_csv_path}")
    print(f"  - Local: {local_csv_path}")
    print(f"  - Original: {original_path}")
    print("📁 Certifique-se de que o arquivo está em src/dataset/PosicaoIntelli.csv")
    return False

def run_sql_setup(db_config):
    """Executa o script SQL de configuração da distribuição automática"""
    print("🗄️  Executando configuração SQL...")
    
    # Detectar se estamos no Docker ou ambiente local
    docker_sql_path = Path('./scripts/03_distribuicao_automatica_clientes.sql')
    local_sql_path = Path('../scripts/03_distribuicao_automatica_clientes.sql')
    
    sql_file = None
    
    # Tentar primeiro o caminho do Docker
    if docker_sql_path.exists():
        sql_file = docker_sql_path
        print(f"✅ Script SQL encontrado (Docker): {sql_file}")
    elif local_sql_path.exists():
        sql_file = local_sql_path
        print(f"✅ Script SQL encontrado (local): {sql_file}")
    else:
        # Tentar caminhos alternativos
        alternative_paths = [
            Path('../../scripts/03_distribuicao_automatica_clientes.sql'),
            Path('../../../scripts/03_distribuicao_automatica_clientes.sql'),
            Path('./03_distribuicao_automatica_clientes.sql')
        ]
        
        for alt_path in alternative_paths:
            if alt_path.exists():
                sql_file = alt_path
                print(f"✅ Script encontrado em: {sql_file}")
                break
        else:
            print("❌ Script SQL não encontrado em nenhum local")
            return False
    
    try:
        # Comando psql
        cmd = [
            'psql',
            f"--host={db_config['host']}",
            f"--port={db_config['port']}", 
            f"--dbname={db_config['database']}",
            f"--username={db_config['user']}",
            '--file', str(sql_file)
        ]
        
        print(f"🔧 Executando comando: {' '.join(cmd)}")
        
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['password']
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Configuração SQL executada com sucesso")
            if result.stdout:
                print(f"📝 Saída: {result.stdout}")
            return True
        else:
            print(f"❌ Erro na execução SQL: {result.stderr}")
            if result.stdout:
                print(f"📝 Saída: {result.stdout}")
            return False
            
    except FileNotFoundError:
        print("❌ psql não encontrado. Instale o PostgreSQL client")
        print("💡 No macOS: brew install postgresql")
        print("💡 No Ubuntu/Debian: apt-get install postgresql-client")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar SQL: {e}")
        return False

def run_population_script():
    """Executa o script de população do banco"""
    print("🚀 Executando população do banco de dados...")
    
    script_path = Path('database_populator.py')
    
    if not script_path.exists():
        print(f"❌ Script de população não encontrado: {script_path}")
        return False
    
    try:
        result = subprocess.run([sys.executable, str(script_path)], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ População do banco concluída com sucesso!")
            print("\n📊 Saída do script:")
            print(result.stdout)
            return True
        else:
            print(f"❌ Erro na população: {result.stderr}")
            print(f"📝 Saída: {result.stdout}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao executar script: {e}")
        return False

def main():
    """Função principal"""
    print("🎯 CONFIGURAÇÃO E POPULAÇÃO DO BANCO DE DADOS")
    print("=" * 50)
    
    # Detectar ambiente
    is_docker = os.getenv('DB_HOST') is not None
    print(f"🔍 Ambiente detectado: {'Docker' if is_docker else 'Local'}")
    
    # 1. Instalar dependências (só se não estiver no Docker)
    if not is_docker:
        if not install_dependencies():
            print("❌ Falha na instalação de dependências")
            return 1
    else:
        print("📦 Dependências já instaladas no Docker")
    
    # 2. Criar/atualizar arquivo de configuração
    if not create_config_file():
        print("❌ Falha na criação do arquivo de configuração")
        return 1
    
    # 3. Verificar arquivo CSV
    if not check_csv_file():
        print("❌ Arquivo CSV não encontrado")
        return 1
    
    # 4. Carregar configuração do banco
    try:
        with open('db_config.json', 'r') as f:
            db_config = json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar configuração: {e}")
        return 1
    
    # 5. Executar configuração SQL
    if not run_sql_setup(db_config):
        print("❌ Falha na configuração SQL")
        return 1
    
    # 6. Executar população
    if not run_population_script():
        print("❌ Falha na população do banco")
        return 1
    
    print("\n🎉 PROCESSO CONCLUÍDO COM SUCESSO!")
    print("\n📋 Próximos passos:")
    print("1. Verifique os dados no banco de dados")
    print("2. Execute consultas de validação")
    print("3. Teste a distribuição automática adicionando novos assessores")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

